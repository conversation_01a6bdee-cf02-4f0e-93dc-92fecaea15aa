/**
 * Result Plan Authentication Manager
 * Handles JWT token management and automatic logout on token expiration
 */
(function($) {
    'use strict';

    class ResultPlanAuthManager {
        constructor() {
            this.apiBaseUrl = '/wp-json/api/';
            this.token = this.getStoredToken();
            this.isLoggedIn = !!this.token;
            this.logoutCallbacks = [];
            
            // Initialize authentication manager
            this.init();
        }

        init() {
            // Set up global AJAX error handler for token expiration
            this.setupGlobalAjaxHandler();
            
            // Check token validity on page load
            if (this.token) {
                this.validateToken();
            }
            
            // Set up periodic token validation (every 30 seconds)
            setInterval(() => {
                if (this.token) {
                    this.validateToken();
                }
            }, 30000);
        }

        /**
         * Set up global AJAX error handler to catch token expiration
         */
        setupGlobalAjaxHandler() {
            const self = this;
            
            // jQuery AJAX global error handler
            $(document).ajaxError(function(event, xhr, settings) {
                if (xhr.status === 401) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error === 'TOKEN_EXPIRED' || response.logout_required) {
                            self.handleTokenExpiration();
                        }
                    } catch (e) {
                        // If response is not JSON, check for common token expiration messages
                        if (xhr.responseText.includes('TOKEN_EXPIRED') || 
                            xhr.responseText.includes('session has expired')) {
                            self.handleTokenExpiration();
                        }
                    }
                }
            });

            // Override jQuery.ajax to automatically add Authorization header
            const originalAjax = $.ajax;
            $.ajax = function(options) {
                if (self.token && options.url && options.url.includes('/wp-json/api/')) {
                    options.headers = options.headers || {};
                    options.headers['Authorization'] = self.token;
                }
                return originalAjax.call(this, options);
            };
        }

        /**
         * Store token in localStorage
         */
        storeToken(token) {
            if (token) {
                localStorage.setItem('result_plan_auth_token', token);
                this.token = token;
                this.isLoggedIn = true;
            }
        }

        /**
         * Get stored token from localStorage
         */
        getStoredToken() {
            return localStorage.getItem('result_plan_auth_token');
        }

        /**
         * Remove token from storage
         */
        clearToken() {
            localStorage.removeItem('result_plan_auth_token');
            this.token = null;
            this.isLoggedIn = false;
        }

        /**
         * Validate current token by making a test API call
         */
        validateToken() {
            if (!this.token) return;

            $.ajax({
                url: this.apiBaseUrl + 'auth/get-profile',
                type: 'GET',
                headers: {
                    'Authorization': this.token
                },
                success: (response) => {
                    // Token is valid
                    console.log('Token validation successful');
                },
                error: (xhr) => {
                    if (xhr.status === 401) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.error === 'TOKEN_EXPIRED' || response.logout_required) {
                                this.handleTokenExpiration();
                            }
                        } catch (e) {
                            // Handle non-JSON error responses
                            this.handleTokenExpiration();
                        }
                    }
                }
            });
        }

        /**
         * Handle token expiration - logout user and redirect
         */
        handleTokenExpiration() {
            console.log('Token expired - logging out user');
            
            // Clear stored token
            this.clearToken();
            
            // Show notification to user
            this.showExpirationNotification();
            
            // Call registered logout callbacks
            this.logoutCallbacks.forEach(callback => {
                try {
                    callback();
                } catch (e) {
                    console.error('Error in logout callback:', e);
                }
            });
            
            // Redirect to login page after a short delay
            setTimeout(() => {
                this.redirectToLogin();
            }, 3000);
        }

        /**
         * Show notification that session has expired
         */
        showExpirationNotification() {
            // Check if toasting library is available
            if (typeof toasting !== 'undefined') {
                toasting.create({
                    type: "warning",
                    title: "Session Expired",
                    text: "Your session has expired. You will be redirected to the login page.",
                    timer: 3000
                });
            } else {
                // Fallback to alert
                alert('Your session has expired. You will be redirected to the login page.');
            }
        }

        /**
         * Redirect to login page
         */
        redirectToLogin() {
            // Try to find login page URL, fallback to WordPress login
            const loginUrl = '/my-account/' || '/wp-login.php';
            window.location.href = loginUrl;
        }

        /**
         * Login user with credentials
         */
        login(email, password) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: this.apiBaseUrl + 'auth/login',
                    type: 'POST',
                    data: {
                        email: email,
                        password: password
                    },
                    success: (response) => {
                        if (response.token) {
                            this.storeToken(response.token);
                            resolve(response);
                        } else {
                            reject('No token received');
                        }
                    },
                    error: (xhr) => {
                        reject(xhr.responseJSON || xhr.responseText);
                    }
                });
            });
        }

        /**
         * Logout user
         */
        logout() {
            return new Promise((resolve, reject) => {
                if (!this.token) {
                    this.clearToken();
                    resolve();
                    return;
                }

                $.ajax({
                    url: this.apiBaseUrl + 'auth/logout',
                    type: 'POST',
                    headers: {
                        'Authorization': this.token
                    },
                    success: (response) => {
                        this.clearToken();
                        resolve(response);
                    },
                    error: (xhr) => {
                        // Even if logout API fails, clear local token
                        this.clearToken();
                        resolve();
                    }
                });
            });
        }

        /**
         * Register callback to be called when user is logged out due to token expiration
         */
        onLogout(callback) {
            if (typeof callback === 'function') {
                this.logoutCallbacks.push(callback);
            }
        }

        /**
         * Check if user is currently logged in
         */
        isUserLoggedIn() {
            return this.isLoggedIn && !!this.token;
        }

        /**
         * Get current token
         */
        getToken() {
            return this.token;
        }
    }

    // Create global instance
    window.ResultPlanAuth = new ResultPlanAuthManager();

    // jQuery ready function to initialize any login forms
    $(document).ready(function() {
        // Auto-handle login forms with class 'result-plan-login-form'
        $('.result-plan-login-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const email = $form.find('input[name="email"], input[name="username"]').val();
            const password = $form.find('input[name="password"]').val();
            
            if (email && password) {
                window.ResultPlanAuth.login(email, password)
                    .then(response => {
                        // Redirect on successful login
                        window.location.href = '/my-account/';
                    })
                    .catch(error => {
                        console.error('Login failed:', error);
                        // Show error message
                        if (typeof toasting !== 'undefined') {
                            toasting.create({
                                type: "error",
                                title: "Login Failed",
                                text: error.message || 'Invalid credentials',
                                timer: 5000
                            });
                        }
                    });
            }
        });

        // Auto-handle logout links with class 'result-plan-logout'
        $('.result-plan-logout').on('click', function(e) {
            e.preventDefault();
            
            window.ResultPlanAuth.logout()
                .then(() => {
                    window.location.href = '/';
                });
        });
    });

})(jQuery);
