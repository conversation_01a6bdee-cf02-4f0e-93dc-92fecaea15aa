<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Result Plan Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .auth-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            background: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Result Plan Authentication Test</h1>
    
    <div class="auth-section">
        <h2>Authentication Status</h2>
        <div id="auth-status" class="status info">
            Checking authentication status...
        </div>
        <button onclick="checkAuthStatus()">Refresh Status</button>
    </div>

    <div class="auth-section">
        <h2>Login</h2>
        <form id="login-form" class="result-plan-login-form">
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required>
            </div>
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
    </div>

    <div class="auth-section">
        <h2>Test API Call</h2>
        <p>This will test an authenticated API endpoint to see if the token is working:</p>
        <button onclick="testApiCall()">Test Get Profile</button>
        <div id="api-result"></div>
    </div>

    <div class="auth-section">
        <h2>Logout</h2>
        <button onclick="logout()" class="result-plan-logout">Logout</button>
    </div>

    <div class="auth-section">
        <h2>Token Expiration Test</h2>
        <p>For testing purposes, you can manually trigger token expiration:</p>
        <button onclick="simulateTokenExpiration()">Simulate Token Expiration</button>
    </div>

    <!-- Include jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Include the auth manager (you'll need to adjust the path) -->
    <script src="js/auth-manager.js"></script>

    <script>
        // Wait for auth manager to be ready
        $(document).ready(function() {
            // Check initial auth status
            checkAuthStatus();

            // Register logout callback
            if (window.ResultPlanAuth) {
                window.ResultPlanAuth.onLogout(function() {
                    updateAuthStatus('User logged out due to token expiration', 'error');
                });
            }

            // Override the default login form handler for this test
            $('#login-form').off('submit').on('submit', function(e) {
                e.preventDefault();
                
                const email = $('#email').val();
                const password = $('#password').val();
                
                if (email && password) {
                    window.ResultPlanAuth.login(email, password)
                        .then(response => {
                            updateAuthStatus('Login successful!', 'success');
                            checkAuthStatus();
                        })
                        .catch(error => {
                            console.error('Login failed:', error);
                            updateAuthStatus('Login failed: ' + (error.message || 'Invalid credentials'), 'error');
                        });
                }
            });
        });

        function checkAuthStatus() {
            if (window.ResultPlanAuth) {
                const isLoggedIn = window.ResultPlanAuth.isUserLoggedIn();
                const token = window.ResultPlanAuth.getToken();
                
                if (isLoggedIn && token) {
                    updateAuthStatus('User is logged in. Token: ' + token.substring(0, 50) + '...', 'success');
                } else {
                    updateAuthStatus('User is not logged in', 'info');
                }
            } else {
                updateAuthStatus('Auth manager not loaded', 'error');
            }
        }

        function updateAuthStatus(message, type) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
        }

        function testApiCall() {
            if (!window.ResultPlanAuth || !window.ResultPlanAuth.isUserLoggedIn()) {
                document.getElementById('api-result').innerHTML = '<div class="status error">Please login first</div>';
                return;
            }

            $.ajax({
                url: '/wp-json/api/auth/get-profile',
                type: 'GET',
                headers: {
                    'Authorization': window.ResultPlanAuth.getToken()
                },
                success: function(response) {
                    document.getElementById('api-result').innerHTML = 
                        '<div class="status success">API call successful!</div>' +
                        '<pre>' + JSON.stringify(response, null, 2) + '</pre>';
                },
                error: function(xhr) {
                    document.getElementById('api-result').innerHTML = 
                        '<div class="status error">API call failed: ' + xhr.status + ' ' + xhr.statusText + '</div>' +
                        '<pre>' + xhr.responseText + '</pre>';
                }
            });
        }

        function logout() {
            if (window.ResultPlanAuth) {
                window.ResultPlanAuth.logout()
                    .then(() => {
                        updateAuthStatus('Logged out successfully', 'info');
                        document.getElementById('api-result').innerHTML = '';
                    });
            }
        }

        function simulateTokenExpiration() {
            // This simulates what happens when a token expires
            if (window.ResultPlanAuth) {
                window.ResultPlanAuth.handleTokenExpiration();
            }
        }
    </script>
</body>
</html>
