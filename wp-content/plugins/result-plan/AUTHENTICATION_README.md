# Result Plan Authentication with Automatic Logout

This document explains the automatic logout functionality implemented for the Result Plan application when JWT tokens expire.

## Overview

The authentication system now includes automatic logout functionality that:
- Detects when JWT tokens expire
- Automatically logs out users when their session expires
- Shows user-friendly notifications
- Redirects users to the login page
- Handles token expiration gracefully across all API calls

## Backend Changes

### 1. Enhanced Token Validation (`class-result-plan-init.php`)

- **Token Expiration Detection**: The `logged_in_user()` method now properly detects expired tokens
- **Database Cleanup**: Expired tokens are marked as logged out in the database
- **Error Response**: Returns specific error codes for token expiration (`TOKEN_EXPIRED`)
- **Token Duration**: Tokens now expire after 24 hours (configurable)

### 2. Improved Logout API (`class-auth.php`)

- **Proper Token Invalidation**: The logout endpoint now marks tokens as logged out in the database
- **JWT Import**: Added proper JWT library import
- **Error Handling**: Handles cases where tokens might already be expired during logout

### 3. Database Schema

The JWT tokens table includes:
- `logged_out` field to track token status
- `updated_at` field for tracking when tokens were invalidated

## Frontend Changes

### 1. Authentication Manager (`auth-manager.js`)

A comprehensive JavaScript class that handles:

#### Features:
- **Global AJAX Handler**: Automatically detects token expiration in all API calls
- **Token Storage**: Manages JWT tokens in localStorage
- **Automatic Logout**: Handles token expiration gracefully
- **User Notifications**: Shows expiration messages using toasting library
- **Periodic Validation**: Checks token validity every 30 seconds
- **Login/Logout Methods**: Provides easy-to-use authentication methods

#### Usage:

```javascript
// Check if user is logged in
if (window.ResultPlanAuth.isUserLoggedIn()) {
    // User is authenticated
}

// Login user
window.ResultPlanAuth.login(email, password)
    .then(response => {
        // Login successful
    })
    .catch(error => {
        // Login failed
    });

// Logout user
window.ResultPlanAuth.logout()
    .then(() => {
        // Logout successful
    });

// Register callback for automatic logout
window.ResultPlanAuth.onLogout(function() {
    // Called when user is automatically logged out due to token expiration
    console.log('User was logged out due to token expiration');
});
```

### 2. Automatic Form Handling

The auth manager automatically handles forms with specific classes:

```html
<!-- Login form - automatically handled -->
<form class="result-plan-login-form">
    <input type="email" name="email" required>
    <input type="password" name="password" required>
    <button type="submit">Login</button>
</form>

<!-- Logout link - automatically handled -->
<a href="#" class="result-plan-logout">Logout</a>
```

## How It Works

### 1. Token Expiration Flow

1. **API Call Made**: User makes an API call to a protected endpoint
2. **Token Validation**: Backend validates the JWT token
3. **Expiration Detected**: If token is expired, backend returns 401 with `TOKEN_EXPIRED` error
4. **Frontend Detection**: Auth manager detects the error response
5. **Automatic Logout**: User is logged out automatically
6. **User Notification**: User sees a notification about session expiration
7. **Redirect**: User is redirected to login page after 3 seconds

### 2. Periodic Token Validation

- Every 30 seconds, the auth manager makes a test API call to validate the token
- If the token is expired, automatic logout is triggered
- This ensures users are logged out even if they're not actively using the application

### 3. Global AJAX Error Handling

- All AJAX calls are automatically monitored for token expiration
- No need to add error handling to each individual API call
- Works with both jQuery AJAX and custom API calls

## Configuration

### Token Expiration Time

To change the token expiration time, modify the `generateToken()` method in `class-result-plan-init.php`:

```php
'exp' => time() + (60 * 60 * 24), // 24 hours
// Change to:
'exp' => time() + (60 * 60 * 2),  // 2 hours
```

### Validation Frequency

To change how often tokens are validated, modify the interval in `auth-manager.js`:

```javascript
// Check token validity every 30 seconds
setInterval(() => {
    if (this.token) {
        this.validateToken();
    }
}, 30000); // Change this value (in milliseconds)
```

### Redirect URL

To change where users are redirected after logout, modify the `redirectToLogin()` method:

```javascript
redirectToLogin() {
    const loginUrl = '/my-account/' || '/wp-login.php';
    window.location.href = loginUrl;
}
```

## Testing

### 1. Test Page

A test page is available at `wp-content/plugins/result-plan/public/test-auth.html` that demonstrates:
- Login functionality
- Token status checking
- API calls with authentication
- Manual logout
- Simulated token expiration

### 2. Manual Testing

1. **Login**: Use the login form to authenticate
2. **Wait for Expiration**: Wait for the token to expire (or simulate it)
3. **Make API Call**: Try to access a protected endpoint
4. **Verify Logout**: Confirm that automatic logout occurs

### 3. Token Expiration Simulation

For testing purposes, you can temporarily set a very short token expiration time (e.g., 1 minute) to quickly test the automatic logout functionality.

## Security Considerations

1. **Token Storage**: Tokens are stored in localStorage (consider httpOnly cookies for enhanced security)
2. **Token Cleanup**: Expired tokens are marked as logged out in the database
3. **Error Messages**: Generic error messages prevent information disclosure
4. **Automatic Cleanup**: Periodic validation ensures expired sessions are cleaned up

## Troubleshooting

### Common Issues:

1. **Auth Manager Not Loading**: Ensure jQuery is loaded before auth-manager.js
2. **API Calls Not Working**: Check that the API base URL is correct
3. **Notifications Not Showing**: Ensure toasting library is available
4. **Redirects Not Working**: Check the login URL configuration

### Debug Mode:

Enable console logging by adding this to auth-manager.js:

```javascript
console.log('Token validation successful');
console.log('Token expired - logging out user');
```

## Future Enhancements

1. **Token Refresh**: Implement automatic token refresh before expiration
2. **Remember Me**: Add option for longer-lasting sessions
3. **Multiple Device Management**: Track and manage tokens across devices
4. **Session Activity**: Log user session activity for security auditing
